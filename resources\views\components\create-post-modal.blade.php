<!-- Create Post Modal -->
<div x-data="createPostModal()" x-show="open" x-cloak class="fixed inset-0 z-50 overflow-y-auto" aria-labelledby="modal-title" role="dialog" aria-modal="true">
    <div class="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
        <!-- Background overlay -->
        <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0" x-transition:enter-end="opacity-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100" x-transition:leave-end="opacity-0" class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" @click="closeModal()"></div>

        <!-- This element is to trick the browser into centering the modal contents. -->
        <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>

        <!-- Modal panel -->
        <div x-show="open" x-transition:enter="ease-out duration-300" x-transition:enter-start="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" x-transition:enter-end="opacity-100 translate-y-0 sm:scale-100" x-transition:leave="ease-in duration-200" x-transition:leave-start="opacity-100 translate-y-0 sm:scale-100" x-transition:leave-end="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95" class="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-2xl sm:w-full">
            
            <!-- Modal Header -->
            <div class="bg-white px-4 pt-5 pb-4 sm:p-6 sm:pb-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="text-lg leading-6 font-medium text-gray-900" id="modal-title">
                        Create Post
                    </h3>
                    <button @click="closeModal()" class="text-gray-400 hover:text-gray-600">
                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>

                <!-- User Info -->
                <div class="flex items-center space-x-3 mb-4">
                    <img class="h-10 w-10 rounded-full" src="{{ auth()->user()->avatar ?? 'https://ui-avatars.com/api/?name=' . urlencode(auth()->user()->name) . '&color=7BC74D&background=EEEEEE' }}" alt="{{ auth()->user()->name }}">
                    <div>
                        <p class="font-medium text-gray-900">{{ auth()->user()->name }}</p>
                        <select x-model="formData.organization_id" class="text-sm text-gray-600 border-0 bg-transparent focus:ring-0 p-0">
                            <option value="">Personal Post</option>
                            @if(auth()->user()->organizations)
                                @foreach(auth()->user()->organizations()->where('status', 'active')->get() as $org)
                                    <option value="{{ $org->id }}">{{ $org->name }}</option>
                                @endforeach
                            @endif
                        </select>
                    </div>
                </div>

                <!-- Form -->
                <form @submit.prevent="submitPost()" enctype="multipart/form-data">
                    @csrf
                    
                    <!-- Post Type -->
                    <div class="mb-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Post Type</label>
                        <select x-model="formData.type" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                            <option value="general">General</option>
                            <option value="announcement">Announcement</option>
                            <option value="event">Event</option>
                            <option value="financial_report">Financial Report</option>
                        </select>
                    </div>

                    <!-- Title -->
                    <div class="mb-4">
                        <input x-model="formData.title" type="text" placeholder="What's the title of your post?" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green text-lg" required>
                    </div>

                    <!-- Content -->
                    <div class="mb-4">
                        <textarea x-model="formData.content" placeholder="What's on your mind?" rows="4" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green resize-none" required></textarea>
                    </div>

                    <!-- Image Upload -->
                    <div class="mb-4">
                        <div class="flex items-center space-x-4">
                            <label class="flex items-center space-x-2 cursor-pointer text-custom-green hover:text-custom-second-darkest">
                                <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                                </svg>
                                <span class="text-sm font-medium">Add Photos</span>
                                <input type="file" @change="handleImageUpload" multiple accept="image/*" class="hidden" name="images[]">
                            </label>
                        </div>
                        
                        <!-- Image Preview -->
                        <div x-show="selectedImages.length > 0" class="mt-3 grid grid-cols-2 gap-2">
                            <template x-for="(image, index) in selectedImages" :key="index">
                                <div class="relative">
                                    <img :src="image.url" class="w-full h-24 object-cover rounded-lg">
                                    <button @click="removeImage(index)" type="button" class="absolute top-1 right-1 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600">
                                        ×
                                    </button>
                                </div>
                            </template>
                        </div>
                    </div>

                    <!-- Facebook Embed URL -->
                    <div class="mb-4">
                        <input x-model="formData.facebook_embed_url" type="url" placeholder="Facebook embed URL (optional)" class="w-full border-gray-300 rounded-md shadow-sm focus:ring-custom-green focus:border-custom-green">
                    </div>

                    <!-- Error Messages -->
                    <div x-show="errors.length > 0" class="mb-4 bg-red-50 border border-red-200 rounded-md p-3">
                        <template x-for="error in errors" :key="error">
                            <p class="text-sm text-red-600" x-text="error"></p>
                        </template>
                    </div>

                    <!-- Modal Footer -->
                    <div class="bg-gray-50 px-4 py-3 sm:px-6 sm:flex sm:flex-row-reverse -mx-4 -mb-4 mt-6">
                        <button type="submit" :disabled="loading" class="w-full inline-flex justify-center rounded-md border border-transparent shadow-sm px-4 py-2 bg-custom-green text-base font-medium text-white hover:bg-custom-second-darkest focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green sm:ml-3 sm:w-auto sm:text-sm disabled:opacity-50">
                            <span x-show="!loading">Post</span>
                            <span x-show="loading" class="flex items-center">
                                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                                Posting...
                            </span>
                        </button>
                        <button @click="closeModal()" type="button" class="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm px-4 py-2 bg-white text-base font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-custom-green sm:mt-0 sm:ml-3 sm:w-auto sm:text-sm">
                            Cancel
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
function createPostModal() {
    return {
        open: false,
        loading: false,
        errors: [],
        selectedImages: [],
        formData: {
            title: '',
            content: '',
            type: 'general',
            organization_id: '',
            facebook_embed_url: '',
            status: 'published'
        },

        openModal() {
            this.open = true;
            this.resetForm();
        },

        closeModal() {
            this.open = false;
            this.resetForm();
        },

        resetForm() {
            this.formData = {
                title: '',
                content: '',
                type: 'general',
                organization_id: '',
                facebook_embed_url: '',
                status: 'published'
            };
            this.selectedImages = [];
            this.errors = [];
            this.loading = false;
        },

        handleImageUpload(event) {
            const files = Array.from(event.target.files);
            files.forEach(file => {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        this.selectedImages.push({
                            file: file,
                            url: e.target.result
                        });
                    };
                    reader.readAsDataURL(file);
                }
            });
        },

        removeImage(index) {
            this.selectedImages.splice(index, 1);
        },

        async submitPost() {
            this.loading = true;
            this.errors = [];

            const formData = new FormData();
            formData.append('_token', document.querySelector('meta[name="csrf-token"]').getAttribute('content'));
            
            Object.keys(this.formData).forEach(key => {
                if (this.formData[key]) {
                    formData.append(key, this.formData[key]);
                }
            });

            this.selectedImages.forEach((image, index) => {
                formData.append(`images[${index}]`, image.file);
            });

            try {
                const response = await fetch('/posts', {
                    method: 'POST',
                    body: formData,
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest',
                    }
                });

                const data = await response.json();

                if (data.success) {
                    this.closeModal();
                    // Refresh the page or update the feed
                    window.location.reload();
                } else {
                    this.errors = data.errors ? Object.values(data.errors).flat() : ['An error occurred'];
                }
            } catch (error) {
                this.errors = ['Network error. Please try again.'];
            } finally {
                this.loading = false;
            }
        }
    }
}

// Global function to open the modal
window.openCreatePostModal = function() {
    Alpine.store('createPostModal', true);
    document.querySelector('[x-data*="createPostModal"]').__x.$data.openModal();
}
</script>
