<?php

namespace App\Http\Controllers;

use App\Models\Post;
use App\Models\Organization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Illuminate\Validation\Rule;

class PostController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $posts = Post::with(['user', 'organization', 'comments', 'likes'])
            ->published()
            ->latest('published_at')
            ->paginate(10);

        return view('posts.index', compact('posts'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $organizations = collect();
        
        // Get organizations where user is a member
        if (auth()->check()) {
            $organizations = auth()->user()->activeOrganizations()->get();
        }

        return view('posts.create', compact('organizations'));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {


        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => ['required', Rule::in(['announcement', 'event', 'financial_report', 'general'])],
            'organization_id' => 'nullable|exists:organizations,id',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'facebook_embed_url' => 'nullable|url',
            'is_pinned' => 'nullable',
            'status' => ['required', Rule::in(['published', 'draft'])],
        ]);

        // Convert empty organization_id to null
        if (empty($validated['organization_id'])) {
            $validated['organization_id'] = null;
        }

        // Check if user can post to the selected organization
        if ($validated['organization_id']) {
            $organization = Organization::findOrFail($validated['organization_id']);
            $membership = $organization->members()->where('user_id', auth()->id())->first();
            
            if (!$membership || $membership->pivot->status !== 'active') {
                return back()->withErrors(['organization_id' => 'You are not authorized to post to this organization.']);
            }
        }

        $validated['user_id'] = auth()->id();
        $validated['published_at'] = $validated['status'] === 'published' ? now() : null;

        // Convert is_pinned to boolean if it exists
        if (isset($validated['is_pinned'])) {
            $validated['is_pinned'] = filter_var($validated['is_pinned'], FILTER_VALIDATE_BOOLEAN);
        } else {
            $validated['is_pinned'] = false;
        }

        // Handle image uploads
        if ($request->hasFile('images')) {
            $imagePaths = [];
            foreach ($request->file('images') as $image) {
                $path = $image->store('posts/images', 'public');
                $imagePaths[] = $path;
            }
            $validated['images'] = $imagePaths;
        }

        $post = Post::create($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Post created successfully!',
                'post' => $post->load(['user', 'organization'])
            ]);
        }

        return redirect()->route('posts.show', $post)
            ->with('success', 'Post created successfully!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Post $post)
    {
        $post->load([
            'user',
            'organization',
            'comments' => function ($query) {
                $query->with('user', 'replies.user')->whereNull('parent_id');
            },
            'likes.user'
        ]);

        return view('posts.show', compact('post'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Post $post)
    {
        // Check if user can edit this post
        if ($post->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $organizations = auth()->user()->activeOrganizations()->get();

        return view('posts.edit', compact('post', 'organizations'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, Post $post)
    {
        // Check if user can edit this post
        if ($post->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        $validated = $request->validate([
            'title' => 'required|string|max:255',
            'content' => 'required|string',
            'type' => ['required', Rule::in(['announcement', 'event', 'financial_report', 'general'])],
            'organization_id' => 'nullable|exists:organizations,id',
            'images' => 'nullable|array|max:5',
            'images.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'facebook_embed_url' => 'nullable|url',
            'is_pinned' => 'boolean',
            'status' => ['required', Rule::in(['published', 'draft'])],
            'remove_images' => 'nullable|array',
            'remove_images.*' => 'string',
        ]);

        // Check organization membership if changing organization
        if ($validated['organization_id'] && $validated['organization_id'] !== $post->organization_id) {
            $organization = Organization::findOrFail($validated['organization_id']);
            $membership = $organization->members()->where('user_id', auth()->id())->first();
            
            if (!$membership || $membership->pivot->status !== 'active') {
                return back()->withErrors(['organization_id' => 'You are not authorized to post to this organization.']);
            }
        }

        // Handle published_at timestamp
        if ($validated['status'] === 'published' && !$post->published_at) {
            $validated['published_at'] = now();
        } elseif ($validated['status'] === 'draft') {
            $validated['published_at'] = null;
        }

        // Handle image removal
        $currentImages = $post->images ?? [];
        if ($request->has('remove_images')) {
            foreach ($request->remove_images as $imageToRemove) {
                if (in_array($imageToRemove, $currentImages)) {
                    Storage::disk('public')->delete($imageToRemove);
                    $currentImages = array_filter($currentImages, fn($img) => $img !== $imageToRemove);
                }
            }
        }

        // Handle new image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $path = $image->store('posts/images', 'public');
                $currentImages[] = $path;
            }
        }

        $validated['images'] = array_values($currentImages);

        $post->update($validated);

        if ($request->expectsJson()) {
            return response()->json([
                'success' => true,
                'message' => 'Post updated successfully!',
                'post' => $post->load(['user', 'organization'])
            ]);
        }

        return redirect()->route('posts.show', $post)
            ->with('success', 'Post updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Post $post)
    {
        // Check if user can delete this post
        if ($post->user_id !== auth()->id() && !auth()->user()->isAdmin()) {
            abort(403, 'Unauthorized action.');
        }

        // Delete associated images
        if ($post->images) {
            foreach ($post->images as $image) {
                Storage::disk('public')->delete($image);
            }
        }

        $post->delete();

        return redirect()->route('dashboard')
            ->with('success', 'Post deleted successfully!');
    }

    /**
     * Toggle like on a post
     */
    public function toggleLike(Post $post)
    {
        $user = auth()->user();
        $like = $post->likes()->where('user_id', $user->id)->first();

        if ($like) {
            $like->delete();
            $liked = false;
        } else {
            $post->likes()->create(['user_id' => $user->id]);
            $liked = true;
        }

        return response()->json([
            'success' => true,
            'liked' => $liked,
            'likes_count' => $post->likes()->count()
        ]);
    }
}
